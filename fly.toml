# fly.toml app configuration file generated for sumopod-backend on 2025-07-10T00:50:44Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'sumopod-backend'
primary_region = 'sin'

[build]

[env]
  PORT = '8080'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
