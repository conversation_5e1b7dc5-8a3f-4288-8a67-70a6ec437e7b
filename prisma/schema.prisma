generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String        @id @default(uuid())
  name          String
  email         String        @unique
  emailVerified Boolean
  image         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @default(now())
  accounts      Account[]
  balances      Balance[]
  payments      Payment[]
  sessions      Session[]
  transactions  Transaction[]

  @@map("user")
}

model Session {
  id        String   @id @default(uuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id @default(uuid())
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @default(now())
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String   @id @default(uuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  @@map("verification")
}

model Balance {
  id          String   @id @default(uuid())
  userId      String?
  userBalance Int?     @default(0)
  createdAt   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  @@map("balances")
}

model Payment {
  id         Int      @id @default(autoincrement())
  userId     String?
  amount     Int
  status     String?  @default("pending")
  invoiceUrl String?
  createdAt  DateTime @default(now())
  user       User?    @relation(fields: [userId], references: [id])

  @@map("payments")
}

model Transaction {
  id     String   @id @default(uuid())
  userId String?
  amount Int
  type   String
  date   DateTime @default(now())
  user   User?    @relation(fields: [userId], references: [id])

  @@map("transactions")
}


