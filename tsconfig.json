{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}